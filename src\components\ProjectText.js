"use client";

import { motion } from 'framer-motion';

const ProjectText = ({ scrollProgress, projects }) => {
  const projectCount = projects.length;
  
  return (
    <div className="h-full flex flex-col justify-center px-8 relative">
      {/* Render all project texts, each handling its own visibility */}
      {projects.map((project, index) => {
        // Calculate this specific project's visibility
        const segmentSize = 1 / projectCount;
        const segmentStart = index * segmentSize;
        const segmentEnd = (index + 1) * segmentSize;
        
        let opacity = 0;
        
        // Fast fade-in: text appears quickly when card is 50% in
        if (scrollProgress >= segmentStart + (segmentSize * 0.5) && scrollProgress < segmentEnd) {
          // Quick fade in over just 10% of segment (from 50% to 60%)
          const fadeInStart = segmentStart + (segmentSize * 0.5);
          const fadeInEnd = segmentStart + (segmentSize * 0.9);
          
          if (scrollProgress <= fadeInEnd) {
            const fadeInProgress = (scrollProgress - fadeInStart) / (segmentSize * 0.3);
            opacity = Math.min(1, fadeInProgress);
          } else {
            // Stay at full opacity from 60% to 100% of segment
            opacity = 1;
          }
        }
        // Fast fade-out: text disappears quickly when next card starts
        else if (scrollProgress >= segmentEnd && index < projectCount - 1) {
          // Quick fade out over just 10% of next segment (from 0% to 10% of next)
          const fadeOutStart = segmentEnd + (segmentSize * 0.3);
          // Fade out ends at 30% of next segment
          const fadeOutEnd = segmentEnd + (segmentSize * 0.5);
          
          if (scrollProgress <= fadeOutEnd) {
            const fadeOutProgress = (scrollProgress - fadeOutStart) / (segmentSize * 0.3);
            opacity = Math.max(0, 1 - fadeOutProgress);
          } else {
            opacity = 0;
          }
        }
        // For the last project, stay visible once it appears
        else if (index === projectCount - 1 && scrollProgress >= segmentStart + (segmentSize * 0.5)) {
          const fadeInStart = segmentStart + (segmentSize * 0.5);
          const fadeInEnd = segmentStart + (segmentSize * 0.6);
          
          if (scrollProgress <= fadeInEnd) {
            const fadeInProgress = (scrollProgress - fadeInStart) / (segmentSize * 0.1);
            opacity = Math.min(1, fadeInProgress);
          } else {
            opacity = 1;
          }
        }
        
        return (
          <div
            key={project.id}
            className="absolute inset-0 w-full h-full flex flex-col justify-center"
            style={{ opacity }}
          >
            {/* Project Label */}
            <div className="mb-4">
              <span className="inline-block bg-primary/20 text-primary px-3 py-1 rounded-full text-sm font-medium">
                {project.label}
              </span>
            </div>
            
            {/* Project Title */}
            <h3 className="font-heading font-bold text-3xl text-primary mb-4">
              {project.title}
            </h3>
            
            {/* Project Description */}
            <p className="text-secondary text-lg mb-6 leading-relaxed">
              {project.description}
            </p>
            
            {/* Tech Stack */}
            <div className="mb-6">
              <h4 className="text-primary font-semibold mb-3">Tech Stack</h4>
              <div className="flex flex-wrap gap-2">
                {project.techStack.map((tech) => (
                  <span 
                    key={tech}
                    className="bg-background border border-primary/30 text-primary px-3 py-1 rounded-lg text-sm"
                  >
                    {tech}
                  </span>
                ))}
              </div>
            </div>
            
            {/* Project Links */}
            <div className="flex gap-4">
              <button className="bg-primary text-background px-6 py-2 rounded-lg font-medium hover:bg-primary/90 transition-colors">
                View Live
              </button>
              <button className="border border-primary text-primary px-6 py-2 rounded-lg font-medium hover:bg-primary/10 transition-colors">
                View Code
              </button>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ProjectText;
