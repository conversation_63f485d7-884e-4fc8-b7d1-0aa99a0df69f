"use client";

import { motion } from 'framer-motion';
import { useRef, useState, useEffect } from 'react';
import ProjectCard from './ProjectCard';
import ProjectText from './ProjectText';

const Projects = () => {
  const sectionRef = useRef(null);
  const [titleVisible, setTitleVisible] = useState(false);
  const [scrollEffects, setScrollEffects] = useState({
    opacity: 1,
    blur: 0,
    scale: 1
  });
  const [projectScrollProgress, setProjectScrollProgress] = useState(0);
  
  // Projects data array
  const projects = [
    {
      id: 1,
      title: "Portfolio Website",
      description: "A modern, responsive portfolio website built with Next.js and Framer Motion. Features smooth animations, scroll-based interactions, and a clean design system.",
      techStack: ['Next.js', 'React', 'Tailwind CSS', 'Framer Motion'],
      label: "Featured Project"
    },
    {
      id: 2,
      title: "E-Commerce Platform",
      description: "Full-stack e-commerce solution with user authentication, payment processing, and admin dashboard. Built for scalability and performance.",
      techStack: ['React', 'Node.js', 'MongoDB', 'Stripe'],
      label: "Full-Stack Project"
    },
    {
      id: 3,
      title: "Task Management App",
      description: "Collaborative task management application with real-time updates, team collaboration features, and intuitive drag-and-drop interface.",
      techStack: ['Vue.js', 'Firebase', 'Vuetify', 'Socket.io'],
      label: "Collaborative App"
    },
    {
      id: 4,
      title: "Weather Dashboard",
      description: "Interactive weather dashboard with location-based forecasts, historical data visualization, and customizable widgets for weather tracking.",
      techStack: ['React', 'D3.js', 'OpenWeather API', 'Chart.js'],
      label: "Data Visualization"
    }
  ];
  
  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return;
      
      // Find the Services section by looking for it in the DOM
      const servicesSection = document.querySelector('[class*="bg-background py-16"]');
      if (!servicesSection) return;
      
      const servicesRect = servicesSection.getBoundingClientRect();
      const projectsRect = sectionRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      
      // Calculate when Services section is mostly scrolled past
      const servicesBottom = servicesRect.bottom;
      const servicesTop = servicesRect.top;
      
      // Show title earlier - changed from 0.3 to 0.5 (title appears when Services bottom is at 50% of screen)
      const triggerPoint = windowHeight * 0.2;
      const servicesAlmostGone = servicesBottom <= triggerPoint;
      const servicesStartedScrolling = servicesTop <= windowHeight * 0.8;
      
      // Title appears when Services is almost gone
      const shouldShowTitle = servicesAlmostGone && servicesStartedScrolling;
      setTitleVisible(shouldShowTitle);
      
      // Calculate scroll-driven effects when title is visible
      if (shouldShowTitle) {
        // Calculate how far we've scrolled into the Projects section
        const projectsTop = projectsRect.top;
        const projectsHeight = projectsRect.height;
        
        // Start project animations when Projects section reaches top of viewport
        const triggerOffset = windowHeight * 0.1;
        if (projectsTop <= triggerOffset) {
          // Calculate scroll progress through Projects section (0 to 1)
          const scrolledIntoProjects = Math.abs(projectsTop - triggerOffset);
          // Use 60% of section height for smoother, more controlled animations
          const scrollProgress = Math.min(1, scrolledIntoProjects / (projectsHeight * 0.6));

          // Apply scroll-driven effects to title with smoother transitions
          const opacity = Math.max(0.05, 1 - (scrollProgress * 3)); // Fade to 5% opacity more gradually
          const blur = scrollProgress * 6; // Blur up to 6px (less aggressive)
          const scale = Math.max(0.8, 1 - (scrollProgress * 0.5)); // Scale down to 80% (less dramatic)

          setScrollEffects({ opacity, blur, scale });

          // Use the SAME scroll progress for project animations - perfect synchronization!
          setProjectScrollProgress(scrollProgress);
        } else {
          // Reset effects when Projects section hasn't reached the trigger point yet
          setScrollEffects({ opacity: 1, blur: 0, scale: 1 });
          setProjectScrollProgress(0);
        }
      } else {
        // Title not visible yet - reset everything
        setProjectScrollProgress(0);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    // Check initial position
    handleScroll();
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  const containerVariants = {
    hidden: {
      transition: {
        staggerChildren: 0.02,
        staggerDirection: -1,
        delayChildren: 0
      }
    },
    visible: {
      transition: {
        staggerChildren: 0.02,
      }
    }
  };
  
  const letterVariants = {
    hidden: {
      opacity: 0,
      y: 40,
      transition: {
        duration: 0.3,
        ease: "easeIn"
      }
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  const letters = "Projects".split("");

  return (
    <>
      {/* Fixed/Centered Title with scroll-driven effects - LOWER Z-INDEX */}
      <motion.div
        className="fixed inset-0 flex items-center justify-center pointer-events-none z-5"
        initial="hidden"
        animate={titleVisible ? "visible" : "hidden"}
        variants={containerVariants}
        style={{
          opacity: titleVisible ? scrollEffects.opacity : 0,
          filter: `blur(${scrollEffects.blur}px)`,
          transform: `scale(${scrollEffects.scale})`
        }}
      >
        <motion.h2 
          className="font-heading font-extrabold text-primary text-4xl lg:text-6xl"
          variants={containerVariants}
        >
          {letters.map((letter, index) => (
            <motion.span
              key={index}
              variants={letterVariants}
              className="inline-block"
            >
              {letter}
            </motion.span>
          ))}
        </motion.h2>
      </motion.div>

      {/* Container for Projects Layout - 75% of screen width, centered */}
      <div
        className="fixed inset-0 flex items-center justify-center pointer-events-none z-10"
        style={{
          opacity: projectScrollProgress > 0 ? 1 : 0
        }}
      >
        {/* Main container - adjust w-3/4 to change overall width (75% currently) */}
        <div className="w-9/10 h-screen flex bg-background pointer-events-auto">
          {/* Project Text - Left Side (1/3 of container) */}
          <div className="w-1/3 h-full flex items-center pl-8 lg:pl-12">
            <div className="w-full">
              <ProjectText scrollProgress={projectScrollProgress} projects={projects} />
            </div>
          </div>

          {/* Project Card - Right Side (2/3 of container) */}
          <div className="w-2/3 h-full flex items-center pr-8 lg:pr-12 overflow-hidden">
            <div className="w-full h-full">
              <ProjectCard scrollProgress={projectScrollProgress} projects={projects} />
            </div>
          </div>
        </div>
      </div>

      {/* Projects Section - provides scroll space */}
      <section 
        ref={sectionRef}
        className="bg-background py-20 min-h-[400vh]"
      >
        <div className="w-full mx-auto px-6">
          {/* Invisible spacer to provide scroll area */}
          <div className="h-[400vh]"></div>
        </div>
      </section>
    </>
  );
};

export default Projects;
